import { request as umiRequest } from '@umijs/max';
import { message } from 'antd';
import { getApiBaseUrl } from './env';

const baseURL = getApiBaseUrl();

interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params?: Record<string, any>;
  data?: any;
  headers?: Record<string, string>;
  skipErrorHandler?: boolean;
  timeout?: number; // 添加超时时间选项
}

/**
 * 统一的请求函数
 * @param url 请求地址
 * @param options 请求选项
 * @returns Promise
 */
export async function request(url: string, options: RequestOptions = {}) {
  try {
    // 构建完整的URL
    const fullUrl = url.startsWith('http') ? url : `${baseURL}${url}`;

    // 默认请求头
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };

    // 如果有token，添加到请求头
    const token = localStorage.getItem('token');
    if (token) {
      defaultHeaders['X-Access-Token'] = token;
    }

    // 合并请求头
    const headers = {
      ...defaultHeaders,
      ...options.headers,
    };

    // 发送请求
    const response = await umiRequest(fullUrl, {
      method: options.method || 'GET',
      params: options.params,
      data: options.data,
      headers,
      skipErrorHandler: options.skipErrorHandler,
      timeout: options.timeout, // 添加超时时间设置
    });

    return response;
  } catch (error: any) {
    // 如果不跳过错误处理，则显示错误信息
    if (!options.skipErrorHandler) {
      // 尝试获取错误响应中的数据
      let errorMessage = '请求失败，请稍后再试';

      if (error.response && error.response.data) {
        const responseData = error.response.data;
        if (responseData.message) {
          errorMessage = responseData.message;
        }
      }

      message.error(errorMessage);
      console.error('Request error:', error);
    }

    // 确保错误对象中包含响应数据，便于调用者处理
    throw error;
  }
}

/**
 * 文件上传请求
 * @param url 请求地址
 * @param formData FormData 对象
 * @param options 请求选项
 * @returns Promise
 */
export async function uploadFile(url: string, formData: FormData, options: RequestOptions = {}) {
  try {
    // 构建完整的URL
    const fullUrl = url.startsWith('http') ? url : `${baseURL}${url}`;

    // 默认请求头
    const defaultHeaders = {
      // 不设置 Content-Type，让浏览器自动设置包含 boundary 的值
    };

    // 如果有token，添加到请求头
    const token = localStorage.getItem('token');
    if (token) {
      defaultHeaders['X-Access-Token'] = token;
    }

    // 添加其他必要的头信息
    const authorization = localStorage.getItem('Authorization') || token;
    if (authorization) {
      defaultHeaders['authorization'] = authorization;
    }

    defaultHeaders['X-Tenant-Id'] = '0';
    defaultHeaders['X-Timestamp'] = new Date().getTime().toString();
    defaultHeaders['X-Version'] = 'v3';

    // 合并请求头
    const headers = {
      ...defaultHeaders,
      ...options.headers,
    };

    // 发送请求
    const response = await umiRequest(fullUrl, {
      method: 'POST',
      data: formData,
      headers,
      skipErrorHandler: options.skipErrorHandler,
      requestType: 'form', // 指定请求类型为 form
    });

    return response;
  } catch (error) {
    // 如果不跳过错误处理，则显示错误信息
    if (!options.skipErrorHandler) {
      message.error('文件上传失败，请稍后再试');
      console.error('Upload error:', error);
    }
    throw error;
  }
}

export default request;
