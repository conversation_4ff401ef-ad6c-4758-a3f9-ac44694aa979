/**
 * @see https://umijs.org/docs/max/access#access
 * */
export default function access(initialState: { currentUser?: any; userPermission?: any } | undefined) {
  const { currentUser, userPermission } = initialState ?? {};

  // 获取菜单权限列表
  const menuPermissions = userPermission?.menu || [];
  console.log('Access: 当前菜单权限列表:', menuPermissions);

  return {
    // 原有的管理员权限
    canAdmin: currentUser && currentUser.access === 'admin',

    // Jeecg 权限控制
    hasPermission: (permission?: string) => {
      // 如果没有指定权限，则默认有权限
      if (!permission) return true;

      // 如果没有权限数据，则没有权限
      if (!userPermission || !userPermission.codeList) return false;

      // 检查是否有指定的权限
      return userPermission.codeList.includes(permission);
    },

    // 菜单权限过滤器
    menuFilter: (route: any) => {
      // 特殊路由始终可见
      if (
        !route.name ||
        route.path === '/user' ||
        route.path === '/' ||
        route.path === '*' ||
        route.path === '/unauthorized'
      ) {
        return true;
      }

      // 检查路由名称是否在允许的菜单列表中
      const hasAccess = menuPermissions.includes(route.name);
      console.log(`Access: 检查菜单 ${route.name} 权限:`, hasAccess);
      return hasAccess;
    },
  };
}
