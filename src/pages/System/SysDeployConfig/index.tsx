import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Form, Input, Switch, Radio, InputNumber, Button, message, Space, Tooltip, Row, Col, Divider, Select } from 'antd';
import { QuestionCircleOutlined, SaveOutlined } from '@ant-design/icons';
import { querySysDeployConfigById, addSysDeployConfig, editSysDeployConfig } from '@/services/system/sysDeployConfig';

const SysDeployConfig: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [configId, setConfigId] = useState<string>('');

  // 获取配置信息
  const fetchConfig = async () => {
    try {
      setLoading(true);
      // 这里假设系统中只有一条配置记录，ID为1
      // 实际使用时可能需要先获取列表，然后获取第一条记录的ID
      const response = await querySysDeployConfigById('1');

      if (response && response.success && response.result) {
        const { id, deployJson } = response.result;
        setConfigId(id);

        // 解析JSON字符串为对象
        if (deployJson) {
          try {
            const configData = JSON.parse(deployJson);
            form.setFieldsValue(configData);
          } catch (error) {
            console.error('解析配置JSON失败:', error);
            message.error('解析配置信息失败');
          }
        }
      }
    } catch (error) {
      console.error('获取配置信息失败:', error);
      message.error('获取配置信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 首次加载时获取配置
  useEffect(() => {
    fetchConfig();
  }, []);

  // 保存配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      setLoading(true);

      // 将表单值转换为JSON字符串
      const deployJson = JSON.stringify(values);

      const params = {
        id: configId,
        deployJson,
        deployType: 1,
      };

      // 根据是否有ID决定是新增还是编辑
      const submitFunc = configId ? editSysDeployConfig : addSysDeployConfig;
      const response = await submitFunc(params);

      if (response && response.success) {
        message.success('系统配置保存成功');

        // 如果是新增，保存后获取新的ID
        if (!configId && response.result) {
          setConfigId(response.result);
        }
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 表单布局
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 14 },
  };

  return (
    <PageContainer title="系统配置">
      <Card loading={loading}>
        <Form
          form={form}
          {...formItemLayout}
          initialValues={{
            nameSwitch: false,
            plateNoMask: false,
            vinNoMask: false,
            phoneSwitch: false,
            queryType: 'db',
            removeQueryMask: false,
            serviceIpType: 'server',
            serverIp: '',
            carInsuranceCount: 0,
            propertyInsuranceCount: 0,
            valueAddedServiceCount: 0,
            newsLikeCountMin: 50,
            newsLikeCountMax: 500,
            clickRate: 0.5,
          }}
        >
          <Divider orientation="left">基础配置</Divider>
          <p style={{ marginBottom: '20px' }}>以下配置用于设置系统的基本功能和显示方式</p>

          <Form.Item
            name="nameSwitch"
            label="姓名切换为先生/女士"
            valuePropName="checked"
            extra="开启后，系统将自动将姓名替换为先生/女士称呼"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="plateNoMask"
            label="车牌号码脱敏"
            valuePropName="checked"
            extra="开启后，系统将对车牌号码进行脱敏处理，如：粤A****8"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="vinNoMask"
            label="车架号脱敏"
            valuePropName="checked"
            extra="开启后，系统将对车架号进行脱敏处理，如：LSVA****9876"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="phoneSwitch"
            label="手机号脱敏"
            valuePropName="checked"
            extra="开启后，系统将对手机号进行脱敏处理，如：138****8888"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="queryType"
            label={
              <span>
                以上数据实现方式
                <Tooltip title="选择数据实现的方式，只能选择一种">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            extra="说明：数据库存储后期无法还原数据"
          >
            <Radio.Group>
              <Radio value="db">数据库存储</Radio>
              <Radio value="query">查询实现脱敏</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.queryType !== currentValues.queryType}
          >
            {({ getFieldValue }) => {
              // 只在选择“查询实现脱敏”时显示“去除查询脱敏”选项
              return getFieldValue('queryType') === 'query' ? (
                <Form.Item
                  name="removeQueryMask"
                  label={
                    <span>
                      去除查询脱敏
                      <Tooltip title="选择是否去除查询脱敏">
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                  valuePropName="checked"
                  extra="说明：需要为查询实现脱敏，已存储为脱敏数据的无法实现"
                >
                  <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          <Form.Item
            name="serviceIpType"
            label={(
              <span>
                客服IP来源
                <Tooltip title="选择客服IP的来源方式，可以使用服务器IP或租户设置中的IP">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            )}
            extra="选择客服IP的来源方式"
          >
            <Select placeholder="请选择客服IP来源">
              <Select.Option value="server">服务器IP</Select.Option>
              <Select.Option value="tenant">租户设置中IP(若租户IP为空取服务器IP)</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.serviceIpType !== currentValues.serviceIpType}
          >
            {({ getFieldValue }) => {
              // 当选择服务器IP或租户IP为空时需要服务器IP，所以始终显示该字段
              return (
                <Form.Item
                  name="serverIp"
                  label="服务器IP"
                  extra="设置服务器的IP地址，当选择租户IP且租户IP为空时使用"
                >
                  <Input placeholder="请输入服务器IP地址" />
                </Form.Item>
              );
            }}
          </Form.Item>

          <Divider orientation="left">聊天源配置</Divider>
          <p style={{ marginBottom: '20px' }}>以下配置用于设置不同业务类型的聊天源数量，需要确保场景库中有足够的场景数量</p>

          <Form.Item
            name="carInsuranceCount"
            label={
              <span>
                车险聊天源总条数
                <Tooltip title="填写数量，系统将会生成对应的条数，需要场景库中相符，比如填写100条，场景库中也需要100个场景">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            extra="填写数量，系统将会生成对应的条数，需要场景库中相符"
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="propertyInsuranceCount"
            label={
              <span>
                财险聊天源总条数
                <Tooltip title="填写数量，系统将会生成对应的条数，需要场景库中相符，比如填写100条，场景库中也需要100个场景">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            extra="填写数量，系统将会生成对应的条数，需要场景库中相符"
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="valueAddedServiceCount"
            label={
              <span>
                增值服务聊天源总条数
                <Tooltip title="填写数量，系统将会生成对应的条数，需要场景库中相符，比如填写100条，场景库中也需要100个场景">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            extra="填写数量，系统将会生成对应的条数，需要场景库中相符"
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Divider orientation="left">资讯配置</Divider>
          <p style={{ marginBottom: '20px' }}>以下配置用于设置资讯相关的参数</p>

          <Form.Item
            label={
              <span>
                资讯点赞数区间
                <Tooltip title="设置资讯点赞数的随机生成区间，系统将在此区间内随机生成点赞数">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
          >
            <Row gutter={8}>
              <Col span={11}>
                <Form.Item
                  name="newsLikeCountMin"
                  noStyle
                  rules={[{ required: true, message: '请输入最小值' }]}
                >
                  <InputNumber
                    placeholder="最小值"
                    min={0}
                    style={{ width: '100%' }}
                    addonAfter="次"
                  />
                </Form.Item>
              </Col>
              <Col span={2} style={{ textAlign: 'center' }}>
                <span>至</span>
              </Col>
              <Col span={11}>
                <Form.Item
                  name="newsLikeCountMax"
                  noStyle
                  rules={[
                    { required: true, message: '请输入最大值' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('newsLikeCountMin') <= value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('最大值必须大于或等于最小值'));
                      },
                    }),
                  ]}
                >
                  <InputNumber
                    placeholder="最大值"
                    min={0}
                    style={{ width: '100%' }}
                    addonAfter="次"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form.Item>

          <Form.Item
            name="clickRate"
            label={
              <span>
                最低点击率
                <Tooltip title="设置资讯的最低点击率，系统将确保点击率不低于此值">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            extra="设置资讯的最低点击率阈值，取值范围0-1"
          >
            <InputNumber
              placeholder="请输入最低点击率"
              min={0}
              max={1}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
              addonAfter="%"
              formatter={value => value ? `${(Number(value) * 100).toFixed(2)}` : ''}
              parser={value => value ? Number(value) / 100 : 0}
            />
          </Form.Item>

          <Divider orientation="left">操作</Divider>
          <Row>
            <Col span={14} offset={6}>
              <Form.Item>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSave}
                    loading={loading}
                    block
                  >
                    保存配置
                  </Button>
                  <div style={{ color: '#999', fontSize: '12px', textAlign: 'center' }}>
                    注意：配置保存后将立即生效
                  </div>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>
    </PageContainer>
  );
};

export default SysDeployConfig;
