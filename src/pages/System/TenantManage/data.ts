import type { ProColumns } from '@ant-design/pro-components';
import type { FormInstance } from 'antd';

// 租户数据类型
export interface Tenant {
  id: string;
  name: string;
  companyLogo?: string;
  clickId?: string;
  trade?: string;
  trade_dictText?: string;
  companySize?: string;
  companySize_dictText?: string;
  houseNumber?: string;
  position?: string;
  position_dictText?: string;
  department?: string;
  department_dictText?: string;
  createBy?: string;
  createBy_dictText?: string;
  status?: number;
  score?: number;
  clickNum?: number;
  regTime?: string;
  busType?: string;
  companyAddress?: string;
  creditCode?: string;
  phone?: string;
  email?: string;
  isTop?: string;
  intro?: string;
  wechatLogo?: string;
  legalPerson?: string; // 法人
  registeredCapital?: string; // 注册资本
}

// 租户用户数据类型
export interface TenantUser {
  id: string;
  username: string;
  realname: string;
  avatar?: string;
  phone?: string;
  email?: string;
  status?: number;
  createTime?: string;
  tenantStatus?: number;
}

// 产品包数据类型
export interface TenantPack {
  id: string;
  packName: string;
  packCode: string;
  status: number;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}

// 表单引用类型
export type FormRef = FormInstance;

// 租户列表列定义
export const tenantColumns: ProColumns<Tenant>[] = [
  {
    title: '租户名称',
    dataIndex: 'name',
    width: 200,
    ellipsis: true,
  },
  {
    title: '租户编号(ID)',
    dataIndex: 'id',
    width: 120,
  },
  {
    title: '创建者',
    dataIndex: 'createBy_dictText',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    valueEnum: {
      0: { text: '冻结', status: 'error' },
      1: { text: '正常', status: 'success' },
    },
  },
  {
    title: '评分',
    dataIndex: 'score',
    width: 80,
  },
  {
    title: '点击数',
    dataIndex: 'clickNum',
    width: 100,
  },
  {
    title: '注册时间',
    dataIndex: 'regTime',
    width: 120,
  },
];

// 租户回收站列表列定义
export const recycleBinColumns: ProColumns<Tenant>[] = [
  {
    title: '租户名称',
    dataIndex: 'name',
    width: 200,
    ellipsis: true,
  },
  {
    title: '租户编号(ID)',
    dataIndex: 'id',
    width: 120,
  },
  {
    title: '门牌号',
    dataIndex: 'houseNumber',
    width: 100,
  },
  {
    title: '删除时间',
    dataIndex: 'updateTime',
    width: 150,
  },
  {
    title: '删除人',
    dataIndex: 'updateBy',
    width: 120,
  },
];

// 租户用户列表列定义
export const tenantUserColumns: ProColumns<TenantUser>[] = [
  {
    title: '用户名',
    dataIndex: 'username',
    width: 120,
  },
  {
    title: '真实姓名',
    dataIndex: 'realname',
    width: 120,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 120,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: 180,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    valueEnum: {
      0: { text: '冻结', status: 'error' },
      1: { text: '正常', status: 'success' },
    },
  },
  {
    title: '租户状态',
    dataIndex: 'tenantStatus',
    width: 100,
    valueEnum: {
      1: { text: '正常', status: 'success' },
      3: { text: '已拒绝', status: 'error' },
      4: { text: '已离开', status: 'default' },
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
];

// 产品包列表列定义
export const tenantPackColumns: ProColumns<TenantPack>[] = [
  {
    title: '产品包名称',
    dataIndex: 'packName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '产品包编码',
    dataIndex: 'packCode',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    valueEnum: {
      0: { text: '禁用', status: 'error' },
      1: { text: '启用', status: 'success' },
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
];
