import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ontainer, ProCard } from '@ant-design/pro-components';
import { Tabs, message, Empty, Checkbox, Space, Typography, Button, Modal, Upload } from 'antd';
import { getTenantList, uploadMultipleConfig } from '@/services/corp/tenantConfig';
import type { Tenant } from './data';
import TenantList from './components/TenantList';
import DailySettingsTab from './components/DailySettingsTab';
import CategorySettingsTab from './components/CategorySettingsTab';
import LinkSettingsTab from './components/LinkSettingsTab';
import BatchImportModal from './components/BatchImportModal';
import { SettingOutlined, LinkOutlined, TagsOutlined, CarOutlined, BankOutlined, GiftOutlined, CloudUploadOutlined, UploadOutlined, ImportOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

const { Text } = Typography;

const TenantConfigPage: React.FC = () => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('daily');

  // 租户类型选择状态
  const [selectedTenantTypes, setSelectedTenantTypes] = useState<number[]>([]); // 默认不选择任何类型

  // 批量导入相关状态
  const [uploadModalVisible, setUploadModalVisible] = useState<boolean>(false);
  const [uploadFile, setUploadFile] = useState<UploadFile | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);

  // 批量导入设置相关状态
  const [batchImportModalVisible, setBatchImportModalVisible] = useState<boolean>(false);

  // 获取租户列表
  const fetchTenants = async () => {
    setLoading(true);
    try {
      console.log('开始获取租户列表，类型:', selectedTenantTypes);
      const response = await getTenantList(selectedTenantTypes);
      console.log('租户列表响应:', response);

      if (response.success) {
        const tenantList = response.result || [];
        setTenants(tenantList);

        // 如果有租户数据，选择第一个
        if (tenantList.length > 0) {
          console.log('选择第一个租户:', tenantList[0]);
          setSelectedTenant(tenantList[0]);
        }
      } else {
        message.error(response.message || '获取租户列表失败');
      }
    } catch (error) {
      console.error('获取租户列表出错:', error);
      message.error('获取租户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取租户列表
  useEffect(() => {
    fetchTenants();
  }, []);

  // 当选中的租户类型变化时，重新获取租户列表
  useEffect(() => {
    fetchTenants();
  }, [selectedTenantTypes]);

  // 处理租户类型选择变化
  const handleTenantTypeChange = (types: number[]) => {
    setSelectedTenantTypes(types);
    setSelectedTenant(null); // 清除当前选中的租户
  };

  // 选择租户
  const handleSelectTenant = (tenant: Tenant) => {
    console.log('选择租户:', tenant);
    // 无论是否是同一个租户，都先设置为null再设置为新值，强制触发重新加载
    setSelectedTenant(null);
    // 使用setTimeout确保状态更新
    setTimeout(() => {
      setSelectedTenant(tenant);
    }, 10);
  };

  // 切换标签页
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 处理批量导入按钮点击
  const handleBatchImportClick = () => {
    setUploadModalVisible(true);
    setUploadFile(null);
  };

  // 处理批量导入设置按钮点击
  const handleBatchImportSettingsClick = () => {
    setBatchImportModalVisible(true);
  };

  // 处理批量导入设置成功
  const handleBatchImportSuccess = () => {
    message.success('批量导入设置成功');
    // 如果当前有选中的租户，刷新其配置
    if (selectedTenant) {
      // 重新选中当前租户以刷新数据
      handleSelectTenant(selectedTenant);
    }
  };

  // 处理文件上传前检查
  const beforeUpload = (file: UploadFile) => {
    const isExcelOrCsv =
      file.type === 'application/vnd.ms-excel' ||
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      /\.(xlsx|xls)$/i.test(file.name);

    if (!isExcelOrCsv) {
      message.error('只能上传 Excel 文件!');
      return Upload.LIST_IGNORE;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件必须小于 10MB!');
      return Upload.LIST_IGNORE;
    }

    setUploadFile(file);
    return false;
  };

  // 处理文件上传变化
  const handleUploadChange: UploadProps['onChange'] = ({ fileList }) => {
    if (fileList.length > 0) {
      setUploadFile(fileList[0]);
    } else {
      setUploadFile(null);
    }
  };

  // 处理批量导入提交
  const handleBatchImport = async () => {
    if (!uploadFile) {
      message.warning('请选择要上传的文件');
      return;
    }

    setUploading(true);
    try {
      const response = await uploadMultipleConfig(uploadFile.originFileObj as File);
      if (response.success) {
        message.success('批量导入成功');
        setUploadModalVisible(false);
        // 刷新租户列表
        fetchTenants();
        // 如果当前有选中的租户，刷新其配置
        if (selectedTenant) {
          // 重新选中当前租户以刷新数据
          handleSelectTenant(selectedTenant);
        }
      } else {
        message.error(response.message || '批量导入失败');
      }
    } catch (error) {
      console.error('批量导入出错:', error);
      message.error('批量导入失败，请稍后再试');
    } finally {
      setUploading(false);
    }
  };

  return (
    <PageContainer
      header={{
        title: '多租户公司配置',
        subTitle: '管理多租户公司的各项配置参数',
        extra: [
          <Button
            key="batchImportSettings"
            style={{ marginRight: 8 }}
            icon={<ImportOutlined />}
            onClick={handleBatchImportSettingsClick}
          >
            批量导入设置
          </Button>,
          <Button
            key="batchImport"
            type="primary"
            icon={<CloudUploadOutlined />}
            onClick={handleBatchImportClick}
          >
            批量导入所有公司配置
          </Button>
        ],
      }}
    >
      <ProCard split="vertical" bordered headerBordered style={{ height: 'calc(100vh - 200px)' }}>
        <ProCard colSpan="280px" className="tenant-list-card" style={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          <div style={{ padding: '0 16px 16px', borderBottom: '1px solid #f0f0f0' }}>
            <div style={{ marginBottom: 12 }}>
              <Text strong>租户类型筛选</Text>
            </div>
            <Checkbox.Group
              value={selectedTenantTypes}
              onChange={(values) => handleTenantTypeChange(values as number[])}
              style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}
            >
              <Checkbox value={0}>
                <Space>
                  <CarOutlined style={{ color: '#1890ff' }} />
                  <span>车险</span>
                </Space>
              </Checkbox>
              <Checkbox value={1}>
                <Space>
                  <BankOutlined style={{ color: '#52c41a' }} />
                  <span>财险</span>
                </Space>
              </Checkbox>
              <Checkbox value={2}>
                <Space>
                  <GiftOutlined style={{ color: '#fa8c16' }} />
                  <span>增值服务</span>
                </Space>
              </Checkbox>
            </Checkbox.Group>
          </div>
          <TenantList
            tenants={tenants}
            loading={loading}
            selectedTenant={selectedTenant}
            onSelectTenant={handleSelectTenant}
          />
        </ProCard>
        <ProCard className="tenant-content-card" style={{ height: '100%', overflow: 'auto' }}>
          {!selectedTenant ? (
            <div style={{ padding: '40px 0', textAlign: 'center' }}>
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  <span>请从左侧选择一个公司进行配置</span>
                }
              />
            </div>
          ) : (
            <Tabs
              activeKey={activeTab}
              onChange={handleTabChange}
              tabBarStyle={{ marginBottom: 24 }}
              size="large"
              tabBarGutter={24}
              items={[
                {
                  key: 'daily',
                  label: (
                    <span>
                      <SettingOutlined />
                      <span style={{ marginLeft: 8 }}>每日设置</span>
                    </span>
                  ),
                  children: (
                    <DailySettingsTab tenantId={selectedTenant.id} tenantName={selectedTenant.name} />
                  ),
                },

                {
                  key: 'link',
                  label: (
                    <span>
                      <LinkOutlined />
                      <span style={{ marginLeft: 8 }}>链接设置</span>
                    </span>
                  ),
                  children: (
                    <LinkSettingsTab tenantId={selectedTenant.id} tenantName={selectedTenant.name} />
                  ),
                },
                {
                  key: 'category',
                  label: (
                    <span>
                      <TagsOutlined />
                      <span style={{ marginLeft: 8 }}>公司信息设置</span>
                    </span>
                  ),
                  children: (
                    <CategorySettingsTab tenantId={selectedTenant.id} tenantName={selectedTenant.name} />
                  ),
                },
              ]}
            />
          )}
        </ProCard>
      </ProCard>

      {/* 批量导入模态框 */}
      <Modal
        title="批量导入所有公司配置"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setUploadModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleBatchImport}
            loading={uploading}
            disabled={!uploadFile}
          >
            开始导入
          </Button>,
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <Text>请上传包含所有公司配置的Excel文件，系统将批量导入所有配置。</Text>
        </div>
        <Upload.Dragger
          name="file"
          fileList={uploadFile ? [uploadFile] : []}
          beforeUpload={beforeUpload}
          onChange={handleUploadChange}
          onRemove={() => setUploadFile(null)}
          maxCount={1}
          accept=".xlsx,.xls"
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">支持 .xlsx, .xls 格式的Excel文件</p>
        </Upload.Dragger>
      </Modal>

      {/* 批量导入设置模态框 */}
      <BatchImportModal
        visible={batchImportModalVisible}
        onCancel={() => setBatchImportModalVisible(false)}
        onSuccess={handleBatchImportSuccess}
      />
    </PageContainer>
  );
};

export default TenantConfigPage;
