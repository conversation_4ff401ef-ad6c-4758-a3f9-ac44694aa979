import React, { useState, useEffect, useRef } from 'react';
import {
  Form,
  Card,
  InputNumber,
  Button,
  Checkbox,
  Space,
  message,
  Row,
  Col,
  Typography,
  Divider,
  Tooltip,
  Badge,
  Tag
} from 'antd';
import { CarOutlined, BankOutlined, GiftOutlined, InfoCircleOutlined, PercentageOutlined, SettingOutlined, EnvironmentOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { addDailyConfig, editDailyConfig, getDailyConfigInfo } from '@/services/corp/tenantConfig';
import type { FormRef, LedgerConfig, CityItem, DailyConfigInfo } from '../data';
import { FormCitySelector } from '@/components/CitySelector';

const { Title, Text } = Typography;

interface DailySettingsTabProps {
  tenantId: number;
  tenantName: string;
}



const DailySettingsTab: React.FC<DailySettingsTabProps> = ({ tenantId, tenantName }) => {
  const [form] = Form.useForm();
  const formRef = useRef<FormRef>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  // 保存配置ID，用于区分新增还是编辑
  const [configId, setConfigId] = useState<string>('');

  // 台账类型选择状态
  const [carLedgerEnabled, setCarLedgerEnabled] = useState<boolean>(false);
  const [financeLedgerEnabled, setFinanceLedgerEnabled] = useState<boolean>(false);
  const [valueAddedLedgerEnabled, setValueAddedLedgerEnabled] = useState<boolean>(false);



  // 获取配置数据
  const fetchConfig = async () => {
    // 允许tenantId为0，但不允许为null或undefined
    if (tenantId === null || tenantId === undefined) return;

    setLoading(true);
    try {
      console.log(`开始获取租户ID ${tenantId} 的每日设置详情`);

      // 获取每日设置详情
      const response = await getDailyConfigInfo(tenantId);
      console.log('获取每日设置详情响应:', response);

      // 无论哪种情况，先重置表单和状态
      form.resetFields();
      setCarLedgerEnabled(false);
      setFinanceLedgerEnabled(false);
      setValueAddedLedgerEnabled(false);
      setConfigId(''); // 重置配置ID

      // 如果返回成功但结果为null，则保持表单为空
      if (response.success && !response.result) {
        console.log('返回结果为null，表单保持为空');
        return;
      }

      // 如果有结果数据
      if (response.success && response.result) {
        const { id, configJson, clickStart, clickEnd } = response.result;

        // 保存配置ID，用于后续编辑
        if (id) {
          setConfigId(id);
          console.log('保存配置ID:', id);
        } else {
          setConfigId('');
          console.log('未找到配置ID，将使用新增接口');
        }

        // 如果configJson为null或空字符串，则使用空对象
        let config = {};
        if (configJson && configJson !== 'null' && configJson !== '{}') {
          try {
            config = JSON.parse(configJson);
          } catch (e) {
            console.error('解析configJson失败:', e);
          }
        }

        console.log('解析后的配置:', config);

        // 准备表单数据
        const formData: any = {};

        // 设置点击数范围
        if (clickStart !== null && clickStart !== undefined) {
          formData.clickStart = clickStart;
        }

        if (clickEnd !== null && clickEnd !== undefined) {
          formData.clickEnd = clickEnd;
        }

        // 处理城市列表
        let cityListData = [];
        if (config.cityList) {
          if (typeof config.cityList === 'string') {
            // 如果是字符串形式（例如："上海市|北京市"）
            cityListData = config.cityList.split('|').filter(Boolean).map(city => `${city}:${city}:1`);
          } else if (Array.isArray(config.cityList)) {
            // 如果是数组形式
            cityListData = config.cityList.map((item: any) => {
              // 处理新的数据结构 (cityCode, cityName, type)
              if (item.cityCode && item.cityName) {
                // 使用原始的cityName，不包含省份名称
                // 如果cityName已经包含省份名称，需要提取出原始名称
                let originalName = item.cityName;

                // 这里我们保持原始名称，不做处理
                // 在UI显示时可以动态拼接省份名称

                return `${item.cityCode}:${originalName}:${item.type}`;
              }
              // 处理旧的数据结构 (city, type)
              else if (item.city) {
                return `${item.city}:${item.city}:${item.type}`;
              }
              // 未知结构，尝试使用可用字段
              return `${item.cityCode || item.city || ''}:${item.cityName || item.city || ''}:${item.type || 1}`;
            });
          }

          if (cityListData.length > 0) {
            formData.cityList = cityListData;
          }
        }

        // 处理台账数据
        if (config.carLedger) {
          formData.carLedger = config.carLedger;
          setCarLedgerEnabled(true);
        }

        if (config.financeLedger) {
          formData.financeLedger = config.financeLedger;
          setFinanceLedgerEnabled(true);
        }

        if (config.valueAddedLedger) {
          formData.valueAddedLedger = config.valueAddedLedger;
          setValueAddedLedgerEnabled(true);
        }

        console.log('设置表单数据:', formData);

        // 设置表单值
        form.setFieldsValue(formData);
      } else {
        // API调用失败，表单保持为空
        console.log('API调用失败，表单保持为空');
      }
    } catch (error) {
      console.error('获取配置数据出错:', error);
      // 出错时表单保持为空
      form.resetFields();
      setCarLedgerEnabled(false);
      setFinanceLedgerEnabled(false);
      setValueAddedLedgerEnabled(false);
    } finally {
      setLoading(false);
    }
  };

  // 当租户ID变化时获取配置
  useEffect(() => {
    // 允许tenantId为0，但不允许为null或undefined
    if (tenantId === 0 || tenantId) {
      console.log(`租户ID变化为 ${tenantId}，开始获取配置`);
      fetchConfig();
    }
  }, [tenantId, form]); // 添加form依赖项，确保表单实例变化时也重新加载



  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);
      console.log('提交表单数据:', values);

      // 处理城市数据
      let cityList;
      if (Array.isArray(values.cityList) && values.cityList.length > 0) {
        // 创建一个映射表，用于存储省份编码与名称的对应关系
        const provinceCodeMap: Record<string, string> = {
          '11': '北京市',
          '12': '天津市',
          '13': '河北省',
          '14': '山西省',
          '15': '内蒙古自治区',
          '21': '辽宁省',
          '22': '吉林省',
          '23': '黑龙江省',
          '31': '上海市',
          '32': '江苏省',
          '33': '浙江省',
          '34': '安徽省',
          '35': '福建省',
          '36': '江西省',
          '37': '山东省',
          '41': '河南省',
          '42': '湖北省',
          '43': '湖南省',
          '44': '广东省',
          '45': '广西壮族自治区',
          '46': '海南省',
          '50': '重庆市',
          '51': '四川省',
          '52': '贵州省',
          '53': '云南省',
          '54': '西藏自治区',
          '61': '陕西省',
          '62': '甘肃省',
          '63': '青海省',
          '64': '宁夏回族自治区',
          '65': '新疆维吾尔自治区',
          '71': '台湾省',
          '81': '香港特别行政区',
          '82': '澳门特别行政区'
        };

        cityList = values.cityList.map((item: string) => {
          const parts = item.split(':');

          // 处理不同格式的值
          let code, name, type;

          if (parts.length === 3) {
            // 后端数据格式: code:name:type
            code = parts[0];
            name = parts[1];
            type = parseInt(parts[2], 10);

            // 注意：这里不再修改name，保持原始名称
            // 在UI显示时可以动态拼接，但存储和传参时使用原始名称

            // 使用新的字段名称结构
            return {
              cityCode: code,
              cityName: name, // 保持原始名称
              type
            };
          } else if (parts.length === 2) {
            // 旧格式: name:type
            name = parts[0];
            type = parseInt(parts[1], 10);

            return {
              cityCode: name, // 旧格式中没有单独的code，使用name作为cityCode
              cityName: name,
              type
            };
          } else {
            // 未知格式，直接使用
            name = parts[0];
            type = 1; // 默认类型为1

            return {
              cityCode: name, // 未知格式中没有单独的code，使用name作为cityCode
              cityName: name,
              type
            };
          }
        });
      } else {
        // 如果不是数组或为空数组，则使用空数组
        cityList = [];
      }

      // 准备台账数据
      let carLedgerData = null;
      let financeLedgerData = null;
      let valueAddedLedgerData = null;

      if (carLedgerEnabled && values.carLedger) {
        carLedgerData = {
          ledgerStart: values.carLedger.ledgerStart || 0,
          ledgerEnd: values.carLedger.ledgerEnd || 0,
          chatUserStart: values.carLedger.chatUserStart || 0,
          chatUserEnd: values.carLedger.chatUserEnd || 0
        };
      }

      if (financeLedgerEnabled && values.financeLedger) {
        financeLedgerData = {
          ledgerStart: values.financeLedger.ledgerStart || 0,
          ledgerEnd: values.financeLedger.ledgerEnd || 0,
          chatUserStart: values.financeLedger.chatUserStart || 0,
          chatUserEnd: values.financeLedger.chatUserEnd || 0
        };
      }

      if (valueAddedLedgerEnabled && values.valueAddedLedger) {
        valueAddedLedgerData = {
          ledgerStart: values.valueAddedLedger.ledgerStart || 0,
          ledgerEnd: values.valueAddedLedger.ledgerEnd || 0,
          chatUserStart: values.valueAddedLedger.chatUserStart || 0,
          chatUserEnd: values.valueAddedLedger.chatUserEnd || 0
        };
      }

      // 构建配置JSON
      const configJson = {
        cityList: cityList.length > 0 ? cityList : [],
        carLedger: carLedgerData,
        financeLedger: financeLedgerData,
        valueAddedLedger: valueAddedLedgerData
      };

      console.log('构建的configJson:', configJson);

      // 构建请求数据
      const requestData: {
        configJson: string;
        clickStart: number;
        clickEnd: number;
        tenantId: number;
        id?: string;
      } = {
        configJson: JSON.stringify(configJson),
        clickStart: values.clickStart || 0,
        clickEnd: values.clickEnd || 0,
        tenantId
      };

      // 如果有ID，将ID添加到请求数据中
      if (configId) {
        requestData.id = configId;
      }

      console.log('发送的请求数据:', requestData);

      // 发送请求
      let response;
      if (configId) {
        // 如果有ID，调用编辑接口
        console.log('调用编辑接口，ID:', configId);
        response = await editDailyConfig(configId, requestData);
      } else {
        // 如果没有ID，调用新增接口
        console.log('调用新增接口');
        response = await addDailyConfig(requestData);
      }

      console.log('保存响应:', response);

      if (response.success) {
        message.success('配置保存成功');
        // 重新加载数据
        fetchConfig();
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('提交表单出错:', error);
      message.error('表单验证失败，请检查输入');
    } finally {
      setSubmitting(false);
    }
  };

  // 渲染台账类型卡片
  const renderLedgerCard = (
    title: string,
    icon: React.ReactNode,
    description: string,
    fieldName: string,
    enabled: boolean,
    setEnabled: (enabled: boolean) => void
  ) => {

    return (
      <ProCard
        hoverable
        bordered
        headerBordered
        style={{ marginBottom: 16 }}
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              checked={enabled}
              onChange={(e) => setEnabled(e.target.checked)}
              style={{ marginRight: 12 }}
            />
            <span style={{ display: 'flex', alignItems: 'center' }}>
              {icon} <span style={{ marginLeft: 8, fontWeight: 500 }}>{title}</span>
            </span>
            {enabled && (
              <Tag color="success" style={{ marginLeft: 8 }}>
                已启用
              </Tag>
            )}
          </div>
        }
        extra={<span style={{ fontSize: 12, color: '#888' }}>{description}</span>}
        bodyStyle={{ padding: enabled ? '16px' : 0, display: enabled ? 'block' : 'none' }}
      >
        {enabled && (
          <>
            <div style={{ marginBottom: 24 }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                <PercentageOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                <Text strong>台账数占比区间</Text>
                <Tooltip title="此类型台账数占总H5点击数的比例范围">
                  <InfoCircleOutlined style={{ marginLeft: 8, color: '#8c8c8c' }} />
                </Tooltip>
                <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center' }}>
                  <Form.Item noStyle shouldUpdate>
                    {({ getFieldValue }) => {
                      const start = getFieldValue([fieldName, 'ledgerStart']) || 0;
                      const end = getFieldValue([fieldName, 'ledgerEnd']) || 0;
                      return <Badge color="#1890ff" text={`${start}% - ${end}%`} />;
                    }}
                  </Form.Item>
                </div>
              </div>

              <Row gutter={16} align="middle">
                <Col span={24}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div style={{ flex: 1 }}>
                      <Form.Item
                        name={[fieldName, 'ledgerStart']}
                        noStyle
                        rules={[{ required: true, message: '请输入最小值' }]}
                      >
                        <InputNumber
                          placeholder="最小值"
                          style={{ width: '100%' }}
                          min={0}
                          max={100}
                          addonAfter="%"
                          precision={0}
                        />
                      </Form.Item>
                    </div>
                    <div style={{ padding: '0 12px', color: '#999' }}>
                      <Text type="secondary">至</Text>
                    </div>
                    <div style={{ flex: 1 }}>
                      <Form.Item
                        name={[fieldName, 'ledgerEnd']}
                        noStyle
                        rules={[{ required: true, message: '请输入最大值' }]}
                      >
                        <InputNumber
                          placeholder="最大值"
                          style={{ width: '100%' }}
                          min={0}
                          max={100}
                          addonAfter="%"
                          precision={0}
                        />
                      </Form.Item>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>

            <div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                <PercentageOutlined style={{ marginRight: 8, color: '#52c41a' }} />
                <Text strong>聊天用户数占比区间</Text>
                <Tooltip title="聊天用户数占此类型台账数的比例范围">
                  <InfoCircleOutlined style={{ marginLeft: 8, color: '#8c8c8c' }} />
                </Tooltip>
                <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center' }}>
                  <Form.Item noStyle shouldUpdate>
                    {({ getFieldValue }) => {
                      const start = getFieldValue([fieldName, 'chatUserStart']) || 0;
                      const end = getFieldValue([fieldName, 'chatUserEnd']) || 0;
                      return <Badge color="#52c41a" text={`${start}% - ${end}%`} />;
                    }}
                  </Form.Item>
                </div>
              </div>

              <Row gutter={16} align="middle">
                <Col span={24}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div style={{ flex: 1 }}>
                      <Form.Item
                        name={[fieldName, 'chatUserStart']}
                        noStyle
                        rules={[{ required: true, message: '请输入最小值' }]}
                      >
                        <InputNumber
                          placeholder="最小值"
                          style={{ width: '100%' }}
                          min={0}
                          max={100}
                          addonAfter="%"
                          precision={0}
                        />
                      </Form.Item>
                    </div>
                    <div style={{ padding: '0 12px', color: '#999' }}>
                      <Text type="secondary">至</Text>
                    </div>
                    <div style={{ flex: 1 }}>
                      <Form.Item
                        name={[fieldName, 'chatUserEnd']}
                        noStyle
                        rules={[{ required: true, message: '请输入最大值' }]}
                      >
                        <InputNumber
                          placeholder="最大值"
                          style={{ width: '100%' }}
                          min={0}
                          max={100}
                          addonAfter="%"
                          precision={0}
                        />
                      </Form.Item>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </>
        )}
      </ProCard>
    );
  };

  return (
    <div style={{ padding: '20px 0' }}>
      <div style={{ marginBottom: 16 }}>
        <Title level={4}>{tenantName} - 每日设置</Title>
      </div>

      <Form
        form={form}
        layout="vertical"
        ref={formRef}
        // 不设置初始值，由API返回数据决定
      >
        <ProCard title="每月基础设置" style={{ marginBottom: 24 }} headerBordered>
          <div style={{
              backgroundColor: '#f0f8ff',
              padding: 16,
              borderRadius: 4,
              border: '1px solid #cce5ff',
              marginBottom: 16
            }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 12 }}>
              <Text strong style={{ fontSize: 16 }}>
                H5 点击数范围
              </Text>
              <Tag color="blue" style={{ marginLeft: 8 }}>关键指标</Tag>
              <Tooltip title="设置每月预期的H5点击总数范围">
                <InfoCircleOutlined style={{ marginLeft: 8, color: '#1890ff' }} />
              </Tooltip>
            </div>

            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => {
                const clickStart = getFieldValue('clickStart') || 0;
                const clickEnd = getFieldValue('clickEnd') || 0;
                return (
                  <>
                    <Row gutter={16} align="middle">
                      <Col span={24}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <div style={{ flex: 1 }}>
                            <Form.Item
                              name="clickStart"
                              noStyle
                              rules={[{ required: true, message: '请输入最小值' }]}
                            >
                              <InputNumber
                                placeholder="最小值"
                                style={{ width: '100%' }}
                                min={0}
                                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                parser={value => value ? value.replace(/\$\s?|(,*)/g, '') : ''}
                              />
                            </Form.Item>
                          </div>
                          <div style={{ padding: '0 12px', color: '#999' }}>
                            <Text type="secondary">至</Text>
                          </div>
                          <div style={{ flex: 1 }}>
                            <Form.Item
                              name="clickEnd"
                              noStyle
                              rules={[{ required: true, message: '请输入最大值' }]}
                            >
                              <InputNumber
                                placeholder="最大值"
                                style={{ width: '100%' }}
                                min={0}
                                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                parser={value => value ? value.replace(/\$\s?|(,*)/g, '') : ''}
                              />
                            </Form.Item>
                          </div>
                        </div>
                      </Col>
                    </Row>
                    <div style={{ marginTop: 8, textAlign: 'center' }}>
                      <Badge color="#1890ff" text={
                        <Text type="secondary">
                          当前设置范围: {clickStart.toLocaleString()} - {clickEnd.toLocaleString()} 次点击
                        </Text>
                      } />
                    </div>
                  </>
                );
              }}
            </Form.Item>
          </div>
        </ProCard>

        <ProCard title="台账类型设置" style={{ marginBottom: 24 }} headerBordered extra={<Text type="secondary">选择需要生成的台账类型</Text>}>
          <Row gutter={16}>
            <Col span={12}>
              {renderLedgerCard(
                '车险台账',
                <CarOutlined />,
                '包含车辆保险相关记录',
                'carLedger',
                carLedgerEnabled,
                setCarLedgerEnabled
              )}
            </Col>
            <Col span={12}>
              {renderLedgerCard(
                '财险台账',
                <BankOutlined />,
                '包含财产保险相关记录',
                'financeLedger',
                financeLedgerEnabled,
                setFinanceLedgerEnabled
              )}
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              {renderLedgerCard(
                '增值服务台账',
                <GiftOutlined />,
                '包含增值服务相关记录',
                'valueAddedLedger',
                valueAddedLedgerEnabled,
                setValueAddedLedgerEnabled
              )}
            </Col>
          </Row>
        </ProCard>

        <ProCard
          title={<>
            <span>业务城市</span>
            <Tag color="blue" style={{ marginLeft: 8 }}>可选</Tag>
            <Tooltip title="选择业务所在的城市">
              <InfoCircleOutlined style={{ marginLeft: 8, color: '#8c8c8c' }} />
            </Tooltip>
          </>}
          headerBordered
          style={{ marginBottom: 24 }}
        >
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
            <EnvironmentOutlined style={{ fontSize: 18, color: '#1890ff', marginRight: 8 }} />
            <Text strong>选择业务开展城市</Text>
          </div>

          <FormCitySelector
            name="cityList"
            placeholder="请选择业务城市"
            showSelected={false}
            required={false}
            useBackendData={true}
            cityData={[]} // 提供一个空数组作为后备数据
          />
        </ProCard>

        <ProCard bordered style={{ marginTop: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text type="secondary">最后更新时间: {new Date().toLocaleString()}</Text>
            </div>
            <Space size="middle">
              <Button>取消</Button>
              <Button type="primary" icon={<SettingOutlined />} onClick={handleSubmit} loading={submitting}>
                保存设置
              </Button>
            </Space>
          </div>
        </ProCard>


      </Form>
    </div>
  );
};

export default DailySettingsTab;
