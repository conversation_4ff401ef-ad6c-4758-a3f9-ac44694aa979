import React, { useState, useEffect } from 'react';
import { <PERSON>Container } from '@ant-design/pro-layout';
import { Card, Table, Button, Space, message, Popconfirm, Input, Form, Row, Col, Select, Tag, Upload, Modal } from 'antd';
import { PlusOutlined, ExportOutlined, ImportOutlined, DeleteOutlined, EditOutlined, EyeOutlined, InboxOutlined } from '@ant-design/icons';
import type { TablePaginationConfig } from 'antd/es/table';
import type { FilterValue } from 'antd/es/table/interface';
import { useAccess, Access } from 'umi';
import { fetchPdSceneList, deletePdScene, batchDeletePdScene } from '@/services/info/pdScene';
import PdSceneModal from './components/PdSceneModal';

const PdSceneList: React.FC = () => {
  // 状态定义
  const [loading, setLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [isUpdate, setIsUpdate] = useState<boolean>(false);
  const [showFooter, setShowFooter] = useState<boolean>(true);
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploading, setUploading] = useState<boolean>(false);
  const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
  const [searchForm] = Form.useForm();

  // 权限控制
  const access = useAccess();

  // 获取列表数据
  const fetchData = async (params: any = {}) => {
    try {
      setLoading(true);
      const { current, pageSize, ...restParams } = params;

      // 添加 sourceType=1 的过滤条件，只显示场景库数据
      const response = await fetchPdSceneList({
        pageNo: current,
        pageSize,
        sourceType: 1,
        ...restParams,
      });

      if (response && response.success) {
        setDataSource(response.result.records || []);
        setPagination({
          ...pagination,
          current,
          pageSize,
          total: response.result.total || 0,
        });
      } else {
        message.error(response.message || '获取数据失败');
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 首次加载数据
  useEffect(() => {
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  }, []);

  // 表格变化处理
  const handleTableChange = (
    newPagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: any,
  ) => {
    fetchData({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
      ...filters,
      ...searchForm.getFieldsValue(),
      orderBy: sorter.field && sorter.order
        ? `${sorter.field} ${sorter.order === 'ascend' ? 'asc' : 'desc'}`
        : undefined,
    });
  };

  // 搜索表单提交
  const onFinish = (values: any) => {
    fetchData({
      current: 1,
      pageSize: pagination.pageSize,
      ...values,
    });
  };

  // 重置搜索表单
  const onReset = () => {
    searchForm.resetFields();
    fetchData({
      current: 1,
      pageSize: pagination.pageSize,
    });
  };

  // 新增按钮点击
  const handleAdd = () => {
    setCurrentRecord({
      sourceType: 1 // 默认设置为场景库类型
    });
    setIsUpdate(false);
    setShowFooter(true);
    setModalVisible(true);
  };

  // 编辑按钮点击
  const handleEdit = (record: any) => {
    setCurrentRecord(record);
    setIsUpdate(true);
    setShowFooter(true);
    setModalVisible(true);
  };

  // 详情按钮点击
  const handleDetail = (record: any) => {
    setCurrentRecord(record);
    setIsUpdate(true);
    setShowFooter(false);
    setModalVisible(true);
  };

  // 删除按钮点击
  const handleDelete = async (id: string) => {
    try {
      const response = await deletePdScene(id);

      if (response && response.success) {
        message.success('删除成功');
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    try {
      const response = await batchDeletePdScene(selectedRowKeys as string[]);

      if (response && response.success) {
        message.success('批量删除成功');
        setSelectedRowKeys([]);
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
      } else {
        message.error(response.message || '批量删除失败');
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  };

  // 导出
  const handleExport = () => {
    // 使用Blob下载文件
    const token = localStorage.getItem('token') || '';
    const headers = new Headers();
    headers.append('X-Access-Token', token);
    headers.append('Content-Type', 'application/json');

    const formValues = searchForm.getFieldsValue();

    fetch('/info/pdScene/exportXls', {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(formValues),
    })
      .then(response => response.blob())
      .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '场景库.xls';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      })
      .catch(error => {
        console.error('导出失败:', error);
        message.error('导出失败');
      });
  };

  // 处理文件上传前检查
  const beforeUpload = (file: any) => {
    const isExcel = file.type === 'application/vnd.ms-excel' ||
                   file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                   /\.(xlsx|xls)$/i.test(file.name);

    if (!isExcel) {
      message.error('只能上传Excel文件!');
      return Upload.LIST_IGNORE;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件必须小于10MB!');
      return Upload.LIST_IGNORE;
    }

    return true;
  };

  // 处理文件选择变化
  const handleFileChange = (info: any) => {
    // 更新文件列表
    let newFileList = [...info.fileList];

    // 限制只能上传一个文件
    newFileList = newFileList.slice(-1);

    // 更新状态
    setFileList(newFileList);
  };

  // 处理导入
  const handleImport = async () => {
    if (fileList.length === 0) {
      message.error('请先选择要上传的文件');
      return;
    }

    const file = fileList[0].originFileObj;
    if (!file) {
      message.error('文件对象无效');
      return;
    }

    // 创建表单数据
    const formData = new FormData();
    formData.append('file', file);

    // 设置上传中状态
    setUploading(true);

    try {
      // 发送请求
      const response = await fetch('/info/pdScene/importExcel?sourceType=1', {
        method: 'POST',
        headers: {
          'X-Access-Token': localStorage.getItem('token') || '',
        },
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        message.success('导入成功');
        // 刷新数据
        fetchData({
          current: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm.getFieldsValue(),
        });
        // 关闭弹窗并清空文件列表
        setImportModalVisible(false);
        setFileList([]);
      } else {
        message.error(result.message || '导入失败');
      }
    } catch (error) {
      console.error('导入出错:', error);
      message.error('导入失败');
    } finally {
      setUploading(false);
    }
  };

  // 模态框关闭后刷新
  const handleSuccess = () => {
    setModalVisible(false);
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm.getFieldsValue(),
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '场景名称',
      dataIndex: 'sceneName',
      key: 'sceneName',
      sorter: true,
    },
    {
      title: '类型',
      dataIndex: 'sourceType',
      key: 'sourceType',
      sorter: true,
      render: (text: number) => {
        return text === 1 ? '场景库' : '姓名库';
      },
    },
    {
      title: '业务类型',
      dataIndex: 'serverType',
      key: 'serverType',
      sorter: true,
      render: (text: string) => {
        if (text === '0') return '车险';
        if (text === '1') return '财险';
        if (text === '2') return '增值服务';
        return text;
      },
    },
    {
      title: '是否使用',
      dataIndex: 'isUse',
      key: 'isUse',
      sorter: true,
      render: (text: string) => {
        if (text === '1') return <Tag color="success">是</Tag>;
        if (text === '0') return <Tag color="default">否</Tag>;
        return <Tag color="default">未知</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="small">
          <Button type="link" size="small" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger>
              删除
            </Button>
          </Popconfirm>
          <Button type="link" size="small" onClick={() => handleDetail(record)}>
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer title="场景库管理">
      <Card>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          name="searchForm"
          onFinish={onFinish}
          style={{ marginBottom: 16 }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="sceneName" label="场景名称" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                <Input placeholder="请输入场景名称" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="sourceType" label="类型" initialValue={1} hidden>
                <Select
                  placeholder="请选择类型"
                  allowClear
                  options={[
                    { label: '场景库', value: 1 },
                    { label: '姓名库', value: 2 },
                  ]}
                />
              </Form.Item>
              <Form.Item name="serverType" label="业务类型" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                <Select
                  placeholder="请选择业务类型"
                  allowClear
                  options={[
                    { label: '车险', value: '0' },
                    { label: '财险', value: '1' },
                    { label: '增值服务', value: '2' },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="isUse" label="是否使用" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                <Select
                  placeholder="请选择是否使用"
                  allowClear
                  options={[
                    { label: '是', value: '1' },
                    { label: '否', value: '0' },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
              <Form.Item style={{ marginBottom: 0, marginRight: 0 }}>
                <Space>
                  <Button type="primary" htmlType="submit">
                    查询
                  </Button>
                  <Button onClick={onReset}>重置</Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {/* 操作按钮 */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              新增
            </Button>
            <Button icon={<ExportOutlined />} onClick={handleExport}>
              导出
            </Button>
            <Button
              icon={<ImportOutlined />}
              onClick={() => {
                // 显示导入弹窗
                setImportModalVisible(true);
              }}
            >
              导入
            </Button>
            {selectedRowKeys.length > 0 && (
              <Popconfirm
                title="确定要删除选中的记录吗?"
                onConfirm={handleBatchDelete}
                okText="确定"
                cancelText="取消"
              >
                <Button danger icon={<DeleteOutlined />}>
                  批量删除
                </Button>
              </Popconfirm>
            )}
          </Space>
        </div>

        {/* 表格 */}
        <div>
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            pagination={pagination}
            onChange={handleTableChange}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
            }}
          />
          <div style={{ marginTop: 16, textAlign: 'right' }}>
            <span>总数量: {pagination.total} 条</span>
          </div>
        </div>
      </Card>

      {/* 表单弹窗 */}
      <PdSceneModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSuccess={handleSuccess}
        record={currentRecord}
        isUpdate={isUpdate}
        showFooter={showFooter}
      />

      {/* 导入弹窗 */}
      <Modal
        title="导入场景库"
        open={importModalVisible}
        onCancel={() => {
          setImportModalVisible(false);
          setFileList([]);
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setImportModalVisible(false);
            setFileList([]);
          }}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleImport}
            loading={uploading}
            disabled={fileList.length === 0}
          >
            确认上传
          </Button>,
        ]}
        width={600}
      >
        <Upload.Dragger
          name="file"
          multiple={false}
          fileList={fileList}
          beforeUpload={beforeUpload}
          onChange={handleFileChange}
          showUploadList={true}
          accept=".xlsx,.xls"
          customRequest={({ file, onSuccess }) => {
            // 只在本地保存文件，不实际上传
            setTimeout(() => {
              onSuccess && onSuccess("ok");
            }, 0);
          }}
          style={{ marginBottom: 16 }}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持 .xlsx, .xls 格式的Excel文件，文件大小不超过10MB
          </p>
        </Upload.Dragger>
      </Modal>
    </PageContainer>
  );
};

export default PdSceneList;
