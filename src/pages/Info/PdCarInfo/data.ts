import type { ProColumns } from '@ant-design/pro-components';
import { isTenantIdZero } from '@/utils/tenant';

// 车险报价信息数据模型
export interface PdCarInfoItem {
  id: string;
  createTime?: string;
  ipAddress?: string;
  guestName?: string;
  city?: string;
  project?: string;
  phoneNumber?: string;
  licensePlateNumber?: string;
  owner?: string;
  engineNumber?: string;
  model?: string;
  useNature?: string;
  vehicleType?: string;
  birthDate?: string;
  vinCode?: string;
  sex?: string;
  ethnicity?: string;
  idNumber?: string;
  tenantId?: string;
  tenantName?: string;
}

// 车险报价信息表格列配置
export const carInfoColumns: ProColumns<PdCarInfoItem>[] = [
  {
    title: '日期',
    dataIndex: 'createTime',
    align: 'center',
    sorter: true,
  },
  {
    title: 'ip地址',
    dataIndex: 'ipAddress',
    align: 'center',
    sorter: true,
  },
  {
    title: '用户名称',
    dataIndex: 'guestName',
    align: 'center',
    sorter: true,
  },
  {
    title: '城市',
    dataIndex: 'city',
    align: 'center',
    sorter: true,
  },
  {
    title: '选择服务',
    dataIndex: 'project',
    align: 'center',
    sorter: true,
  },
  {
    title: '手机号',
    dataIndex: 'phoneNumber',
    align: 'center',
  },
  {
    title: '车牌号',
    dataIndex: 'licensePlateNumber',
    align: 'center',
    sorter: true,
  },
  {
    title: '所有人',
    dataIndex: 'owner',
    align: 'center',
    sorter: true,
  },
  {
    title: '厂牌型号',
    dataIndex: 'model',
    align: 'center',
    sorter: true,
  },
  {
    title: '车架号',
    dataIndex: 'vinCode',
    align: 'center',
    sorter: true,
  },
  {
    title: '性别',
    dataIndex: 'sex',
    align: 'center',
    sorter: true,
  },
];

// 如果是超级管理员，添加主体名称列
if (isTenantIdZero()) {
  carInfoColumns.push({
    title: '主体名称',
    dataIndex: 'tenantName',
    align: 'center',
    sorter: true,
  });
}

// 搜索表单配置
export const searchFormConfig = {
  labelWidth: 100,
  columns: [
    {
      title: '车牌/所有人',
      dataIndex: 'keyword',
      valueType: 'input',
      formItemProps: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
      },
      fieldProps: {
        placeholder: '请输入车牌号或所有人',
      },
    },
    {
      title: '所属公司',
      dataIndex: 'tenantId',
      valueType: 'select',
      formItemProps: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
      },
      fieldProps: {
        placeholder: '请输入公司名称',
        showSearch: true,
        allowClear: true,
      },
      hideInForm: !isTenantIdZero(),
    },
    {
      title: '日期范围',
      dataIndex: 'dateRange',
      valueType: 'dateRange',
      formItemProps: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
      },
      fieldProps: {
        format: 'YYYY-MM-DD',
        placeholder: ['开始日期', '结束日期'],
      },
    },
  ],
};

// 表单字段配置
export const formFieldsConfig = [
  {
    label: '车牌号',
    name: 'licensePlateNumber',
    type: 'input',
    rules: [{ required: false, message: '请输入车牌号' }],
  },
  {
    label: '手机号',
    name: 'phoneNumber',
    type: 'input',
    rules: [{ required: false, message: '请输入手机号' }],
  },
  {
    label: '所有人',
    name: 'owner',
    type: 'input',
    rules: [{ required: false, message: '请输入所有人' }],
  },
  {
    label: '发动机号码',
    name: 'engineNumber',
    type: 'input',
    rules: [{ required: false, message: '请输入发动机号码' }],
  },
  {
    label: '品牌型号',
    name: 'model',
    type: 'input',
    rules: [{ required: false, message: '请输入品牌型号' }],
  },
  {
    label: '车辆类型',
    name: 'vehicleType',
    type: 'input',
    rules: [{ required: false, message: '请输入车辆类型' }],
  },
  {
    label: '身份证号码',
    name: 'idNumber',
    type: 'input',
    rules: [{ required: false, message: '请输入身份证号码' }],
  },
  {
    label: '出生日期',
    name: 'birthDate',
    type: 'input',
    rules: [{ required: false, message: '请输入出生日期' }],
  },
  {
    label: '车辆识别代码',
    name: 'vinCode',
    type: 'input',
    rules: [{ required: false, message: '请输入车辆识别代码' }],
  },
  {
    label: '性别',
    name: 'sex',
    type: 'input',
    rules: [{ required: false, message: '请输入性别' }],
  },
  {
    label: '所属公司',
    name: 'tenantId',
    type: 'select',
    rules: [{ required: false, message: '请选择所属公司' }],
    hidden: !isTenantIdZero(),
  },
];
