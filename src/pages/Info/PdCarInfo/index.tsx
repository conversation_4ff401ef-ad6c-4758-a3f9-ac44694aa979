import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space, Dropdown } from 'antd';
import { DeleteOutlined, DownOutlined } from '@ant-design/icons';
import { carInfoColumns, PdCarInfoItem } from './data';
import {
  getPdCarInfoList,
  deletePdCarInfo,
  batchDeletePdCarInfo,
  getExportUrl,
} from '@/services/info/pdCarInfo';
import PdCarInfoModal from './components/PdCarInfoModal';
import PdCarImportModal from './components/PdCarImportModal';

const PdCarInfoList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<PdCarInfoItem | undefined>(undefined);
  const [modalTitle, setModalTitle] = useState<string>('新增');
  const [showFooter, setShowFooter] = useState<boolean>(true);

  // 刷新表格
  const refreshTable = () => {
    if (actionRef.current) {
      actionRef.current.reload();
      setSelectedRowKeys([]);
    }
  };

  // 打开编辑弹窗
  const handleEdit = (record: PdCarInfoItem) => {
    setCurrentRecord(record);
    setModalTitle('编辑');
    setShowFooter(true);
    setModalVisible(true);
  };

  // 打开详情弹窗
  const handleDetail = (record: PdCarInfoItem) => {
    setCurrentRecord(record);
    setModalTitle('详情');
    setShowFooter(false);
    setModalVisible(true);
  };

  // 删除记录
  const handleDelete = async (record: PdCarInfoItem) => {
    try {
      const response = await deletePdCarInfo(record.id);
      if (response.success) {
        message.success('删除成功');
        refreshTable();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 批量删除记录
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    try {
      const response = await batchDeletePdCarInfo(selectedRowKeys);
      if (response.success) {
        message.success('批量删除成功');
        refreshTable();
      } else {
        message.error(response.message || '批量删除失败');
      }
    } catch (error) {
      message.error('批量删除失败');
    }
  };

  // 打开导入弹窗
  const handleImport = () => {
    setImportModalVisible(true);
  };

  // 导出数据
  const handleExport = () => {
    const url = getExportUrl();
    window.open(url);
  };

  // 表格列配置
  const columns: ProColumns<PdCarInfoItem>[] = [
    ...carInfoColumns,
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => [
        <a key="edit" onClick={() => handleEdit(record)}>
          编辑
        </a>,
        <Dropdown
          key="more"
          menu={{
            items: [
              {
                key: 'detail',
                label: <a onClick={() => handleDetail(record)}>详情</a>,
              },
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    title="是否确认删除"
                    onConfirm={() => handleDelete(record)}
                    placement="topLeft"
                  >
                    <a>删除</a>
                  </Popconfirm>
                ),
              },
            ],
          }}
        >
          <a>
            更多 <DownOutlined />
          </a>
        </Dropdown>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<PdCarInfoItem>
        headerTitle="车险报价信息"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          selectedRowKeys.length > 0 && (
            <Popconfirm
              key="batchDelete"
              title="是否确认删除选中记录?"
              onConfirm={handleBatchDelete}
            >
              <Button danger icon={<DeleteOutlined />}>
                批量删除
              </Button>
            </Popconfirm>
          ),
          <Button key="import" onClick={handleImport}>
            导入
          </Button>,
          <Button key="export" onClick={handleExport}>
            导出
          </Button>,
        ]}
        request={async (params) => {
          // 处理日期范围
          const { dateRange, ...restParams } = params;
          let queryParams = { ...restParams };
          
          if (dateRange && Array.isArray(dateRange) && dateRange.length === 2) {
            queryParams.beginDate = dateRange[0];
            queryParams.endDate = dateRange[1];
          }
          
          const response = await getPdCarInfoList(queryParams);
          
          return {
            data: response.result?.records || [],
            success: response.success,
            total: response.result?.total || 0,
          };
        }}
        columns={columns}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as string[]),
        }}
        pagination={{
          pageSize: 10,
        }}
      />

      {/* 编辑/详情弹窗 */}
      <PdCarInfoModal
        visible={modalVisible}
        title={modalTitle}
        record={currentRecord}
        onCancel={() => setModalVisible(false)}
        onSuccess={() => {
          setModalVisible(false);
          refreshTable();
        }}
        showFooter={showFooter}
      />

      {/* 导入弹窗 */}
      <PdCarImportModal
        visible={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        onSuccess={refreshTable}
      />
    </PageContainer>
  );
};

export default PdCarInfoList;
