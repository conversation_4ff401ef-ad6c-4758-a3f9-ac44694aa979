/**
 * 公司配置文件
 * 用于确定当前运行的是哪个公司的版本
 *
 * 开发环境下可以手动修改此值进行测试
 * 生产环境下应该在构建时通过替换此文件来设置
 */

// 当前公司标识，直接硬编码在此文件中
// 在开发时可以手动修改此值进行测试
// 在构建时，会通过脚本替换此文件中的值

// 将此值更改为 'taiyi' 或 'xudong'
export const CURRENT_COMPANY = 'taiyi';
console.log('当前使用的公司标识:', CURRENT_COMPANY);

// 确保在构建时正确设置公司标识
if (process.env.REACT_APP_COMPANY) {
  console.log('从环境变量获取公司标识:', process.env.REACT_APP_COMPANY);
}

// 公司类型
export type CompanyType = 'taiyi' | 'xudong';

// 公司名称映射
export const COMPANY_NAMES: Record<CompanyType, string> = {
  taiyi: '太一',
  xudong: '旭东',
};

// 获取当前公司标识
export const getCurrentCompany = (): string => {
  return CURRENT_COMPANY;
};

// 获取当前公司名称
export const getCurrentCompanyName = (): string => {
  return COMPANY_NAMES[CURRENT_COMPANY as CompanyType] || COMPANY_NAMES.taiyi;
};
