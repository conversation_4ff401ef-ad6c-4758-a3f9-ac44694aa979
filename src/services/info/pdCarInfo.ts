// 车险报价信息相关API
import { request } from '@umijs/max';

// API路径
const API = {
  list: '/info/pdCarInfo/list',
  add: '/info/pdCarInfo/add',
  edit: '/info/pdCarInfo/edit',
  delete: '/info/pdCarInfo/delete',
  deleteBatch: '/info/pdCarInfo/deleteBatch',
  importExcel: '/info/pdCarInfo/importExcel',
  exportXls: '/info/pdCarInfo/exportXls',
};

/**
 * 查询车险报价信息列表
 * @param params 查询参数
 */
export async function getPdCarInfoList(params: any) {
  // 分离分页参数和其他参数
  const { current, pageSize, ...restParams } = params;
  // 构建包含路径参数的 URL
  const url = `${API.list}/${current}/${pageSize}`;
  return request(url, {
    method: 'POST',
    data: restParams,
  });
}

/**
 * 添加车险报价信息
 * @param data 表单数据
 */
export async function addPdCarInfo(data: any) {
  return request(API.add, {
    method: 'POST',
    data,
  });
}

/**
 * 编辑车险报价信息
 * @param data 表单数据
 */
export async function editPdCarInfo(data: any) {
  return request(API.edit, {
    method: 'POST',
    data,
  });
}

/**
 * 删除车险报价信息
 * @param id 记录ID
 */
export async function deletePdCarInfo(id: string) {
  return request(API.delete, {
    method: 'DELETE',
    params: { id },
  });
}

/**
 * 批量删除车险报价信息
 * @param ids 记录ID数组
 */
export async function batchDeletePdCarInfo(ids: string[]) {
  return request(API.deleteBatch, {
    method: 'DELETE',
    data: { ids },
  });
}

/**
 * 导出车险报价信息
 */
export function getExportUrl() {
  return API.exportXls;
}

/**
 * 导入车险报价信息
 */
export function getImportUrl() {
  return API.importExcel;
}

/**
 * 上传导入文件
 * @param file 文件对象
 * @param data 其他参数
 */
export async function uploadImportFile(file: File, data: any) {
  const formData = new FormData();
  formData.append('file', file);
  
  // 添加其他参数
  Object.keys(data).forEach(key => {
    if (data[key] !== undefined && data[key] !== null) {
      formData.append(key, data[key].toString());
    }
  });
  
  return request(API.importExcel, {
    method: 'POST',
    data: formData,
    requestType: 'form',
  });
}
