// 租户管理相关API
import { request } from '@umijs/max';

// 获取租户列表
export async function getTenantList(params?: any) {
  return request('/sys/tenant/list', {
    method: 'GET',
    params,
  });
}

// 获取租户详情
export async function getTenantById(id: string) {
  try {
    return await request('/sys/tenant/queryById', {
      method: 'GET',
      params: { id, _t: new Date().getTime() }, // 添加时间戳防止缓存
    });
  } catch (error) {
    console.error('获取租户详情API调用失败:', error);
    // 返回一个空的成功响应，避免显示错误消息
    return { success: true, result: null };
  }
}

// 新增租户
export async function addTenant(data: any) {
  return request('/sys/tenant/add', {
    method: 'POST',
    data,
  });
}

// 编辑租户
export async function editTenant(data: any) {
  return request('/sys/tenant/edit', {
    method: 'POST',
    data,
  });
}

// 删除租户（移入回收站）
export async function deleteTenant(id: string) {
  return request('/sys/tenant/delete', {
    method: 'DELETE',
    params: { id },
  });
}

// 批量删除租户
export async function batchDeleteTenant(ids: string[]) {
  return request('/sys/tenant/deleteBatch', {
    method: 'DELETE',
    data: ids,
  });
}

// 获取当前用户的租户列表
export async function getCurrentUserTenants() {
  return request('/sys/tenant/getCurrentUserTenant', {
    method: 'GET',
  });
}

// 邀请用户加入租户
export async function invitationUserJoin(params: any) {
  return request('/sys/tenant/invitationUserJoin', {
    method: 'PUT',
    params,
  });
}

// 获取租户用户列表
export async function getTenantUserList(params: any) {
  return request('/sys/tenant/getTenantUserList', {
    method: 'GET',
    params,
  });
}

// 用户离开租户
export async function leaveTenant(params: any) {
  return request('/sys/tenant/leaveTenant', {
    method: 'PUT',
    params,
  });
}

// 获取产品包列表
export async function getPackList(params?: any) {
  return request('/sys/tenant/packList', {
    method: 'GET',
    params,
  });
}

// 添加产品包权限
export async function addPackPermission(data: any) {
  return request('/sys/tenant/addPackPermission', {
    method: 'POST',
    data,
  });
}

// 编辑产品包权限
export async function editPackPermission(data: any) {
  return request('/sys/tenant/editPackPermission', {
    method: 'PUT',
    data,
  });
}

// 删除租户产品包
export async function deleteTenantPack(id: string) {
  return request('/sys/tenant/deleteTenantPack', {
    method: 'DELETE',
    params: { id },
  });
}

// 获取租户回收站列表
export async function getRecycleBinPageList(params?: any) {
  return request('/sys/tenant/recycleBinPageList', {
    method: 'GET',
    params,
  });
}

// 彻底删除租户
export async function deleteLogicDeleted(id: string) {
  return request('/sys/tenant/deleteLogicDeleted', {
    method: 'DELETE',
    params: { id },
  });
}

// 还原租户
export async function revertTenantLogic(id: string) {
  return request('/sys/tenant/revertTenantLogic', {
    method: 'PUT',
    params: { id },
  });
}

// 获取租户产品包下的用户
export async function queryTenantPackUserList(params?: any) {
  return request('/sys/tenant/queryTenantPackUserList', {
    method: 'GET',
    params,
  });
}

// 移除用户和产品包的关系
export async function deleteTenantPackUser(params: any) {
  return request('/sys/tenant/deleteTenantPackUser', {
    method: 'PUT',
    params,
  });
}

// 添加用户和产品包的关系
export async function addTenantPackUser(data: any) {
  return request('/sys/tenant/addTenantPackUser', {
    method: 'POST',
    data,
  });
}

// 获取用户租户列表
export async function getTenantPageListByUserId(params?: any) {
  return request('/sys/tenant/getTenantPageListByUserId', {
    method: 'GET',
    params,
  });
}

// 获取当前登录租户名称
export async function getLoginTenantName(tenantId: string) {
  if (tenantId) {
    const result = await getTenantById(tenantId);
    if (result && result.success && result.result) {
      return result.result.name;
    }
  }
  return "空";
}

// 保存或更新租户用户
export async function saveOrUpdateTenantUser(data: any, isUpdate: boolean) {
  const url = isUpdate ? '/sys/user/editTenantUser' : '/sys/user/add';
  return request(url, {
    method: 'POST',
    data,
  });
}
