import { request } from '@/utils/request';

// API 路径
const API = {
  add: '/wechat/sysDeployConfig/add',
  edit: '/wechat/sysDeployConfig/edit',
  queryById: '/wechat/sysDeployConfig/queryById',
};

/**
 * 添加系统配置
 * @param params 表单数据
 */
export async function addSysDeployConfig(params: any) {
  return request(API.add, {
    method: 'POST',
    data: params,
  });
}

/**
 * 编辑系统配置
 * @param params 表单数据
 */
export async function editSysDeployConfig(params: any) {
  return request(API.edit, {
    method: 'POST',
    data: params,
  });
}

/**
 * 通过ID查询系统配置
 * @param id 记录ID
 */
export async function querySysDeployConfigById(id: string) {
  return request(API.queryById, {
    method: 'GET',
    params: { id },
  });
}
