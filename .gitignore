# 依赖目录
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log
.pnpm-debug.log

# 构建输出
dist/
build/
out/
.output/
.umi/
.umi-production/

# Electron 打包输出
release/
*.dmg
*.exe
*.AppImage
*.deb
*.rpm
*.snap
*.blockmap
*.zip

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs/
*.log

# IDE和编辑器配置
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.project
.classpath
.settings/
*.sublime-project
*.sublime-workspace

# 缓存和临时文件
.cache/
.temp/
.tmp/
tmp/
temp/
coverage/
.eslintcache
.stylelintcache

# 其他常见忽略文件
.thumbs.db
Thumbs.db
*.bak
*.tmp
*.swp
*~

# 本地配置文件
.npmrc
.yarnrc
.nvmrc

# 自动生成的文件
auto-imports.d.ts
components.d.ts

# 打包分析报告
stats.html
report.html

# 浏览器兼容性文件
.browserslistrc

# 前端框架特有文件
.nuxt/
.next/
.vitepress/dist
.vitepress/cache
.docusaurus/

# 测试相关
__snapshots__/
.nyc_output/
.jest-cache/

# 包管理器特有文件
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# 本地开发环境
.env.*.local

# 其他常见前端工具生成的文件
.history/
.sourcemaps/
.turbo

# 旧管理后台代码（仅用于参考）
src/legacy-admin/

# Electron 相关
electron-builder.yml
electron-builder.yaml
builder-effective-config.yaml
.electron-vue/
main.js

# 保留 electron 目录
!electron/
!public/resource/applogo/
!scripts/optimize-assets.js

# 其他打包相关
package-lock.json
pnpm-lock.yaml
yarn.lock

# 临时文件
*.bak
*.swp
*.swo
*~
._*

# 系统文件
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# 调试文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
